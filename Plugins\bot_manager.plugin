import json
import time
import threading
import requests
from typing import Dict, Any, List, Optional
from base_plugin import <PERSON>Plug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>trategy
from client_utils import run_on_queue, get_last_fragment
from ui.settings import Header, Input, Switch, Text, Divider
from ui.bulletin import BulletinHelper
from android_utils import run_on_ui_thread, log

# Import helper modules
try:
    from bot_interface_helper import create_bot_interface, BotCommands, BotMessage
    HELPER_AVAILABLE = True
except ImportError:
    log("[BotManager] Helper module not available, using basic functionality")
    HELPER_AVAILABLE = False

# Java imports for custom dialogs
try:
    from hook_utils import find_class
    from java import dynamic_proxy
    from org.telegram.messenger import AndroidUtilities

    EditText = find_class("android.widget.EditText")
    LinearLayout = find_class("android.widget.LinearLayout")
    LayoutParams = find_class("android.widget.LinearLayout$LayoutParams")
    TextView = find_class("android.widget.TextView")
    Button = find_class("android.widget.Button")
    ScrollView = find_class("android.widget.ScrollView")
    View = find_class("android.view.View")
    Gravity = find_class("android.view.Gravity")
    Color = find_class("android.graphics.Color")
    Typeface = find_class("android.graphics.Typeface")
    GradientDrawable = find_class("android.graphics.drawable.GradientDrawable")
    StateListDrawable = find_class("android.graphics.drawable.StateListDrawable")

    UI_AVAILABLE = True
except ImportError:
    log("[BotManager] Android UI classes not available")
    UI_AVAILABLE = False
    EditText = LinearLayout = LayoutParams = TextView = Button = ScrollView = View = None
    Gravity = Color = Typeface = GradientDrawable = StateListDrawable = dynamic_proxy = AndroidUtilities = None

__id__ = "bot_manager"
__name__ = "Bot Manager"
__description__ = "Manage Telegram bot with separate interface for receiving and sending messages"
__author__ = "@exteraDev"
__version__ = "1.0.0"
__min_version__ = "11.12.1"
__icon__ = "msg_bot"

# Theme colors for UI
class Theme:
    DIALOG_BG_COLOR = Color.rgb(30, 35, 40) if Color else 0
    PRIMARY_TEXT_COLOR = Color.WHITE if Color else 0
    SECONDARY_TEXT_COLOR = Color.rgb(170, 170, 170) if Color else 0
    HINT_COLOR = Color.rgb(120, 120, 120) if Color else 0
    INPUT_BG_COLOR = Color.rgb(45, 50, 55) if Color else 0
    INPUT_STROKE_COLOR = Color.rgb(80, 85, 90) if Color else 0
    ACCENT_BLUE = Color.rgb(65, 150, 240) if Color else 0
    ACCENT_BLUE_PRESSED = Color.rgb(85, 170, 255) if Color else 0
    BUTTON_SECONDARY_BG = Color.rgb(50, 55, 60) if Color else 0
    BUTTON_SECONDARY_BG_PRESSED = Color.rgb(70, 75, 80) if Color else 0

# Click listener for buttons
class _ClickListener(dynamic_proxy(View.OnClickListener) if dynamic_proxy and View else object):
    def __init__(self, handler_func):
        if dynamic_proxy and View:
            super().__init__()
        self.handler = handler_func

    def onClick(self, view):
        if self.handler:
            self.handler(view)

class BotManagerPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.bot_token = ""
        self.is_polling = False
        self.last_update_id = 0
        self.messages: List[Dict[str, Any]] = []
        self.polling_thread = None
        self.bot_info = None

        # Enhanced interface
        if HELPER_AVAILABLE:
            self.bot_interface = create_bot_interface(self)
            self.bot_commands = BotCommands(self.bot_interface)
        else:
            self.bot_interface = None
            self.bot_commands = None
        
    def on_plugin_load(self):
        """Initialize the plugin when loaded"""
        self.bot_token = self.get_setting("bot_token", "")
        if self.bot_token:
            self._start_polling()
        log("[BotManager] Plugin loaded successfully")

    def on_plugin_unload(self):
        """Cleanup when plugin is unloaded"""
        self._stop_polling()
        log("[BotManager] Plugin unloaded")

    # UI Helper methods
    def _get_button_drawable(self, normal_color, pressed_color):
        """Create button background drawable"""
        if not StateListDrawable or not GradientDrawable:
            return None

        state_list = StateListDrawable()
        normal_drawable = GradientDrawable()
        normal_drawable.setColor(normal_color)
        normal_drawable.setCornerRadius(AndroidUtilities.dp(8))

        pressed_drawable = GradientDrawable()
        pressed_drawable.setColor(pressed_color)
        pressed_drawable.setCornerRadius(AndroidUtilities.dp(8))

        state_list.addState([16842919], pressed_drawable)  # android.R.attr.state_pressed
        state_list.addState([], normal_drawable)
        return state_list

    def _create_styled_button(self, context, text: str, is_primary: bool, on_click):
        """Create styled button"""
        if not Button:
            return None

        button = Button(context)
        button.setText(text)
        button.setOnClickListener(_ClickListener(on_click))
        button.setTextColor(Theme.PRIMARY_TEXT_COLOR)
        button.setTypeface(None, Typeface.BOLD if Typeface else None)
        button.setAllCaps(False)
        button.setPadding(AndroidUtilities.dp(16), AndroidUtilities.dp(12), AndroidUtilities.dp(16), AndroidUtilities.dp(12))

        if is_primary:
            button.setBackground(self._get_button_drawable(Theme.ACCENT_BLUE, Theme.ACCENT_BLUE_PRESSED))
        else:
            button.setBackground(self._get_button_drawable(Theme.BUTTON_SECONDARY_BG, Theme.BUTTON_SECONDARY_BG_PRESSED))
        return button

    def _create_styled_edit_text(self, context, hint: str):
        """Create styled edit text"""
        if not EditText:
            return None

        input_field = EditText(context)
        input_field.setHint(hint)
        input_field.setTextColor(Theme.PRIMARY_TEXT_COLOR)
        input_field.setHintTextColor(Theme.HINT_COLOR)
        input_field.setPadding(AndroidUtilities.dp(12), AndroidUtilities.dp(10), AndroidUtilities.dp(12), AndroidUtilities.dp(10))

        if GradientDrawable:
            bg = GradientDrawable()
            bg.setColor(Theme.INPUT_BG_COLOR)
            bg.setCornerRadius(AndroidUtilities.dp(8))
            bg.setStroke(AndroidUtilities.dp(1), Theme.INPUT_STROKE_COLOR)
            input_field.setBackground(bg)

        return input_field
        
    def create_settings(self):
        """Create the settings UI"""
        return [
            Header(text="Bot Manager Settings"),
            Input(
                key="bot_token",
                text="Bot Token",
                icon="msg_pin_code",
                default="",
                subtext="Enter your Telegram bot token from @BotFather"
            ),
            Switch(
                key="auto_start",
                text="Auto Start Polling",
                icon="msg_timer",
                default=True,
                subtext="Automatically start polling when plugin loads"
            ),
            Switch(
                key="show_notifications",
                text="Show Message Notifications",
                icon="msg_info",
                default=True,
                subtext="Show bulletin notifications for new messages"
            ),
            Switch(
                key="auto_respond",
                text="Auto-respond to Commands",
                icon="msg_bot",
                default=True,
                subtext="Automatically respond to bot commands like /start, /help"
            ),
            Switch(
                key="filter_bot_messages",
                text="Filter Bot Messages",
                icon="msg_block",
                default=True,
                subtext="Ignore messages from other bots"
            ),
            Divider(),
            Text(
                text="Test Bot Connection",
                icon="msg_bot",
                accent=True,
                on_click=self._test_bot_connection
            ),
            Text(
                text="Open Bot Interface",
                icon="msg_forward",
                accent=True,
                on_click=self._open_bot_interface
            ),
            Text(
                text="Start Polling",
                icon="msg_timer",
                on_click=self._start_polling_manual
            ),
            Text(
                text="Stop Polling",
                icon="msg_timer",
                red=True,
                on_click=self._stop_polling_manual
            ),
            Divider(text="How to use:"),
            Text(
                text="1. Get bot token from @BotFather\n2. Enter token above\n3. Test connection\n4. Open bot interface to manage messages\n5. Start polling to receive messages",
                icon="msg_help"
            ),
            Divider(text="Message Flow:"),
            Text(
                text="📥 INCOMING: Users → Your Bot (received via Bot API)\n📤 OUTGOING: Your Bot → Users (sent via Bot API)\n\nThe plugin only processes messages TO your bot, not from regular Telegram chats.",
                icon="msg_info"
            )
        ]
        
    def _test_bot_connection(self, view):
        """Test if the bot token is valid"""
        def test():
            try:
                token = self.get_setting("bot_token", "").strip()
                if not token:
                    run_on_ui_thread(lambda: BulletinHelper.show_error("❌ Bot token not configured"))
                    return
                    
                # Test getMe API call
                url = f"https://api.telegram.org/bot{token}/getMe"
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("ok"):
                        bot_info = data.get("result", {})
                        bot_name = bot_info.get("first_name", "Unknown")
                        bot_username = bot_info.get("username", "")
                        self.bot_info = bot_info
                        
                        message = f"✅ Bot connected: {bot_name}"
                        if bot_username:
                            message += f" (@{bot_username})"
                            
                        run_on_ui_thread(lambda: BulletinHelper.show_success(message))
                    else:
                        error_msg = data.get("description", "Unknown error")
                        run_on_ui_thread(lambda: BulletinHelper.show_error(f"❌ Bot API error: {error_msg}"))
                else:
                    run_on_ui_thread(lambda: BulletinHelper.show_error(f"❌ HTTP error: {response.status_code}"))
                    
            except Exception as e:
                log(f"[BotManager] Error testing bot connection: {e}")
                run_on_ui_thread(lambda: BulletinHelper.show_error(f"❌ Connection error: {str(e)}"))
                
        run_on_queue(test)
        
    def _start_polling_manual(self, view):
        """Manually start polling"""
        token = self.get_setting("bot_token", "").strip()
        if not token:
            BulletinHelper.show_error("❌ Bot token not configured")
            return
            
        if self.is_polling:
            BulletinHelper.show_info("ℹ️ Polling already running")
            return
            
        self._start_polling()
        BulletinHelper.show_success("✅ Polling started")
        
    def _stop_polling_manual(self, view):
        """Manually stop polling"""
        if not self.is_polling:
            BulletinHelper.show_info("ℹ️ Polling not running")
            return
            
        self._stop_polling()
        BulletinHelper.show_success("✅ Polling stopped")
        
    def _start_polling(self):
        """Start polling for bot updates"""
        if self.is_polling:
            return
            
        token = self.get_setting("bot_token", "").strip()
        if not token:
            return
            
        self.bot_token = token
        self.is_polling = True
        
        def poll():
            while self.is_polling:
                try:
                    self._poll_updates()
                    time.sleep(1)  # Poll every second
                except Exception as e:
                    log(f"[BotManager] Polling error: {e}")
                    time.sleep(5)  # Wait longer on error
                    
        self.polling_thread = threading.Thread(target=poll, daemon=True)
        self.polling_thread.start()
        log("[BotManager] Started polling")
        
    def _stop_polling(self):
        """Stop polling for bot updates"""
        self.is_polling = False
        if self.polling_thread:
            self.polling_thread.join(timeout=2)
        log("[BotManager] Stopped polling")
        
    def _poll_updates(self):
        """Poll for new updates from Telegram Bot API"""
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/getUpdates"
            params = {
                "offset": self.last_update_id + 1,
                "limit": 10,
                "timeout": 5
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("ok"):
                    updates = data.get("result", [])
                    for update in updates:
                        self._process_update(update)
                        self.last_update_id = max(self.last_update_id, update.get("update_id", 0))
                        
        except Exception as e:
            log(f"[BotManager] Error polling updates: {e}")
            
    def _process_update(self, update: Dict[str, Any]):
        """Process a single update from Telegram"""
        try:
            # Handle different types of updates
            if "message" in update:
                message = update["message"]
                # Only process incoming messages to the bot (not outgoing from bot)
                if not message.get("from", {}).get("is_bot", False):
                    self._handle_incoming_message(message)

            elif "edited_message" in update:
                # Handle edited messages if needed
                edited_message = update["edited_message"]
                if not edited_message.get("from", {}).get("is_bot", False):
                    log(f"[BotManager] Message edited by user: {edited_message.get('message_id')}")

            elif "callback_query" in update:
                # Handle inline keyboard callbacks if needed
                callback = update["callback_query"]
                log(f"[BotManager] Callback query received: {callback.get('data')}")

            else:
                log(f"[BotManager] Unhandled update type: {list(update.keys())}")

        except Exception as e:
            log(f"[BotManager] Error processing update: {e}")
            
    def _handle_incoming_message(self, message: Dict[str, Any]):
        """Handle incoming message from user to bot"""
        try:
            # Get message details for logging
            sender = message.get("from", {})
            sender_name = sender.get("first_name", "Unknown")
            sender_id = sender.get("id", 0)
            chat = message.get("chat", {})
            chat_id = chat.get("id", 0)
            chat_type = chat.get("type", "unknown")
            text = message.get("text", "[Media/No text]")

            # Log incoming message details
            log(f"[BotManager] Incoming message: {sender_name} (ID: {sender_id}) in {chat_type} chat {chat_id}: {text[:100]}")

            # Filter out unwanted messages if needed
            if self.get_setting("filter_bot_messages", True):
                if sender.get("is_bot", False):
                    log(f"[BotManager] Ignoring message from bot: {sender_name}")
                    return

            # Store message
            self.messages.append({
                "type": "incoming",
                "message": message,
                "timestamp": time.time()
            })

            # Process with enhanced interface if available
            if self.bot_interface:
                self.bot_interface.process_message(message, "incoming")

            # Auto-respond to commands if enabled
            if self.get_setting("auto_respond", True) and self.bot_commands:
                if text.startswith("/"):
                    log(f"[BotManager] Processing command: {text}")
                    bot_message = BotMessage(message, "incoming")
                    response = self.bot_commands.process_command(bot_message)
                    if response:
                        log(f"[BotManager] Sending auto-response: {response[:50]}")
                        self._send_message_to_chat(chat_id, response)

            # Show notification if enabled
            if self.get_setting("show_notifications", True):
                notification_text = f"📨 {sender_name}: {text[:50]}"
                if len(text) > 50:
                    notification_text += "..."

                run_on_ui_thread(lambda: BulletinHelper.show_info(notification_text))

        except Exception as e:
            log(f"[BotManager] Error handling incoming message: {e}")

    def _handle_outgoing_message(self, sent_message: Dict[str, Any], original_text: str):
        """Handle outgoing message sent by bot"""
        try:
            chat_id = sent_message.get("chat", {}).get("id", 0)
            message_id = sent_message.get("message_id", 0)

            log(f"[BotManager] Outgoing message: Bot sent to chat {chat_id} (msg_id: {message_id}): {original_text[:100]}")

            # Store outgoing message
            self.messages.append({
                "type": "outgoing",
                "message": sent_message,
                "timestamp": time.time(),
                "original_text": original_text  # Store original text for reference
            })

            # Process with enhanced interface if available
            if self.bot_interface:
                self.bot_interface.process_message(sent_message, "outgoing")

        except Exception as e:
            log(f"[BotManager] Error handling outgoing message: {e}")

    def _open_bot_interface(self, view):
        """Open the bot management interface"""
        if not UI_AVAILABLE:
            # Fallback to simple dialog
            self._show_simple_interface()
            return

        def dialog_action():
            try:
                fragment = get_last_fragment()
                context = fragment.getParentActivity() if fragment else None
                if not context:
                    BulletinHelper.show_error("❌ Could not get current context")
                    return

                from ui.alert import AlertDialogBuilder
                builder = AlertDialogBuilder(context)
                builder.set_title("🤖 Bot Manager")

                # Create main layout
                main_layout = LinearLayout(context)
                main_layout.setOrientation(LinearLayout.VERTICAL)
                padding = AndroidUtilities.dp(20)
                main_layout.setPadding(padding, AndroidUtilities.dp(16), padding, AndroidUtilities.dp(8))

                # Bot status section
                status_text = TextView(context)
                status_info = self._get_status_text()
                status_text.setText(status_info)
                status_text.setTextColor(Theme.PRIMARY_TEXT_COLOR)
                status_text.setTextSize(14)
                main_layout.addView(status_text)

                # Add space
                space = View(context)
                space.setLayoutParams(LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(16)))
                main_layout.addView(space)

                # Messages section
                if self.messages:
                    messages_scroll = self._create_messages_view(context)
                    if messages_scroll:
                        scroll_params = LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(200))
                        messages_scroll.setLayoutParams(scroll_params)
                        main_layout.addView(messages_scroll)

                # Buttons section
                buttons_layout = LinearLayout(context)
                buttons_layout.setOrientation(LinearLayout.HORIZONTAL)
                buttons_layout.setGravity(Gravity.END)
                buttons_layout.setPadding(0, AndroidUtilities.dp(16), 0, 0)

                dialog_instance = builder.get_dialog()

                def close_action(v):
                    if dialog_instance:
                        dialog_instance.dismiss()

                def send_action(v):
                    if dialog_instance:
                        dialog_instance.dismiss()
                    self._show_send_dialog()

                def stats_action(v):
                    if dialog_instance:
                        dialog_instance.dismiss()
                    self._show_detailed_stats()

                send_button = self._create_styled_button(context, "Send Message", True, send_action)
                stats_button = self._create_styled_button(context, "Stats", False, stats_action)
                close_button = self._create_styled_button(context, "Close", False, close_action)

                btn_params = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                btn_params.setMarginStart(AndroidUtilities.dp(8))

                buttons_layout.addView(send_button)
                buttons_layout.addView(stats_button, btn_params)
                buttons_layout.addView(close_button, btn_params)
                main_layout.addView(buttons_layout)

                builder.set_view(main_layout)
                dialog_instance = builder.show().get_dialog()
                if dialog_instance and dialog_instance.getWindow():
                    dialog_instance.getWindow().getDecorView().setBackgroundColor(Theme.DIALOG_BG_COLOR)

            except Exception as e:
                log(f"[BotManager] Error creating bot interface: {e}")
                BulletinHelper.show_error(f"❌ Error: {str(e)}")

        run_on_ui_thread(dialog_action)

    def _get_status_text(self):
        """Get formatted status text"""
        status_lines = []

        # Bot info
        if self.bot_info:
            bot_name = self.bot_info.get("first_name", "Unknown")
            bot_username = self.bot_info.get("username", "")
            status_lines.append(f"Bot: {bot_name}")
            if bot_username:
                status_lines[-1] += f" (@{bot_username})"

        # Status
        status_lines.append(f"Status: {'🟢 Running' if self.is_polling else '🔴 Stopped'}")
        status_lines.append(f"Messages: {len(self.messages)}")

        # Enhanced stats if available
        if self.bot_interface:
            stats = self.bot_interface.get_stats()
            status_lines.append(f"Chats: {stats['total_chats']}")
            status_lines.append(f"Active chats: {stats['active_chats']}")

        return "\n".join(status_lines)

    def _create_messages_view(self, context):
        """Create scrollable messages view"""
        if not ScrollView or not self.messages:
            return None

        scroll_view = ScrollView(context)
        content_layout = LinearLayout(context)
        content_layout.setOrientation(LinearLayout.VERTICAL)
        content_layout.setPadding(AndroidUtilities.dp(8), AndroidUtilities.dp(8), AndroidUtilities.dp(8), AndroidUtilities.dp(8))

        # Add recent messages
        recent_messages = self.messages[-10:]  # Last 10 messages
        for i, msg_data in enumerate(recent_messages):
            msg = msg_data["message"]
            sender = msg.get("from", {}).get("first_name", "Unknown")
            text = msg.get("text", "[Media]")
            msg_type = "📤" if msg_data.get("type") == "outgoing" else "📥"

            message_text = TextView(context)
            message_text.setText(f"{msg_type} {sender}: {text[:50]}{'...' if len(text) > 50 else ''}")
            message_text.setTextColor(Theme.SECONDARY_TEXT_COLOR)
            message_text.setTextSize(12)
            message_text.setPadding(0, AndroidUtilities.dp(2), 0, AndroidUtilities.dp(2))
            content_layout.addView(message_text)

        scroll_view.addView(content_layout)
        return scroll_view

    def _show_simple_interface(self):
        """Fallback simple interface"""
        try:
            from ui.alert import AlertDialogBuilder

            dialog_text = "🤖 Bot Manager Interface\n\n"
            dialog_text += self._get_status_text()

            if self.messages:
                dialog_text += "\n\n📋 Recent Messages:\n"
                for msg_data in self.messages[-5:]:
                    msg = msg_data["message"]
                    sender = msg.get("from", {}).get("first_name", "Unknown")
                    text = msg.get("text", "[Media]")
                    msg_type = "📤" if msg_data.get("type") == "outgoing" else "📥"
                    dialog_text += f"{msg_type} {sender}: {text[:25]}...\n"

            builder = AlertDialogBuilder(dialog_text)
            builder.set_positive_button("Send Message", lambda: self._show_send_dialog())
            builder.set_neutral_button("View Stats", lambda: self._show_detailed_stats())
            builder.set_negative_button("Close", None)
            builder.show()

        except Exception as e:
            log(f"[BotManager] Error showing simple interface: {e}")
            BulletinHelper.show_error(f"❌ Error: {str(e)}")

    def _show_detailed_stats(self):
        """Show detailed bot statistics"""
        if not UI_AVAILABLE:
            self._show_simple_stats()
            return

        def dialog_action():
            try:
                fragment = get_last_fragment()
                context = fragment.getParentActivity() if fragment else None
                if not context:
                    BulletinHelper.show_error("❌ Could not get current context")
                    return

                from ui.alert import AlertDialogBuilder
                builder = AlertDialogBuilder(context)
                builder.set_title("📊 Bot Statistics")

                # Create main layout
                main_layout = LinearLayout(context)
                main_layout.setOrientation(LinearLayout.VERTICAL)

                # Create scrollable content
                scroll_view = ScrollView(context)
                scroll_params = LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(300))
                scroll_view.setLayoutParams(scroll_params)

                content_layout = LinearLayout(context)
                content_layout.setOrientation(LinearLayout.VERTICAL)
                padding = AndroidUtilities.dp(20)
                content_layout.setPadding(padding, AndroidUtilities.dp(16), padding, AndroidUtilities.dp(8))

                # Stats text
                stats_text = TextView(context)
                stats_content = self._get_detailed_stats_text()
                stats_text.setText(stats_content)
                stats_text.setTextColor(Theme.PRIMARY_TEXT_COLOR)
                stats_text.setTextSize(14)
                content_layout.addView(stats_text)

                scroll_view.addView(content_layout)
                main_layout.addView(scroll_view)

                # Buttons
                buttons_layout = LinearLayout(context)
                buttons_layout.setOrientation(LinearLayout.HORIZONTAL)
                buttons_layout.setGravity(Gravity.END)
                buttons_layout.setPadding(padding, AndroidUtilities.dp(16), padding, AndroidUtilities.dp(8))

                dialog_instance = builder.get_dialog()

                def close_action(v):
                    if dialog_instance:
                        dialog_instance.dismiss()

                close_button = self._create_styled_button(context, "OK", True, close_action)
                buttons_layout.addView(close_button)
                main_layout.addView(buttons_layout)

                builder.set_view(main_layout)
                dialog_instance = builder.show().get_dialog()
                if dialog_instance and dialog_instance.getWindow():
                    dialog_instance.getWindow().getDecorView().setBackgroundColor(Theme.DIALOG_BG_COLOR)

            except Exception as e:
                log(f"[BotManager] Error creating stats dialog: {e}")
                BulletinHelper.show_error(f"❌ Error: {str(e)}")

        run_on_ui_thread(dialog_action)

    def _get_detailed_stats_text(self):
        """Get detailed stats as formatted text"""
        stats_lines = []

        # Basic stats
        stats_lines.append("📊 Basic Statistics:")
        stats_lines.append(f"Total messages: {len(self.messages)}")
        stats_lines.append(f"Polling status: {'Active' if self.is_polling else 'Inactive'}")
        stats_lines.append(f"Last update ID: {self.last_update_id}")

        # Enhanced stats if available
        if self.bot_interface:
            stats = self.bot_interface.get_stats()
            stats_lines.append(f"Total chats: {stats['total_chats']}")
            stats_lines.append(f"Active chats (1h): {stats['active_chats']}")

            # Message type breakdown
            incoming_count = len([m for m in self.messages if m.get("type") == "incoming"])
            outgoing_count = len([m for m in self.messages if m.get("type") == "outgoing"])
            stats_lines.append("")
            stats_lines.append("📈 Message breakdown:")
            stats_lines.append(f"📥 Incoming: {incoming_count}")
            stats_lines.append(f"📤 Outgoing: {outgoing_count}")

        # Bot info
        if self.bot_info:
            stats_lines.append("")
            stats_lines.append("🤖 Bot information:")
            stats_lines.append(f"Name: {self.bot_info.get('first_name', 'Unknown')}")
            if self.bot_info.get("username"):
                stats_lines.append(f"Username: @{self.bot_info['username']}")
            stats_lines.append(f"Can join groups: {self.bot_info.get('can_join_groups', False)}")
            stats_lines.append(f"Supports inline: {self.bot_info.get('supports_inline_queries', False)}")

        return "\n".join(stats_lines)

    def _show_simple_stats(self):
        """Fallback simple stats dialog"""
        try:
            from ui.alert import AlertDialogBuilder

            stats_text = self._get_detailed_stats_text()
            builder = AlertDialogBuilder(stats_text)
            builder.set_positive_button("OK", None)
            builder.show()

        except Exception as e:
            log(f"[BotManager] Error showing simple stats: {e}")
            BulletinHelper.show_error(f"❌ Error: {str(e)}")
            
    def _show_send_dialog(self):
        """Show dialog to send message"""
        if not UI_AVAILABLE:
            self._show_simple_send_dialog()
            return

        def dialog_action():
            try:
                fragment = get_last_fragment()
                context = fragment.getParentActivity() if fragment else None
                if not context:
                    BulletinHelper.show_error("❌ Could not get current context")
                    return

                from ui.alert import AlertDialogBuilder
                builder = AlertDialogBuilder(context)
                builder.set_title("📤 Send Message")

                # Create main layout
                layout = LinearLayout(context)
                layout.setOrientation(LinearLayout.VERTICAL)
                padding = AndroidUtilities.dp(20)
                layout.setPadding(padding, AndroidUtilities.dp(16), padding, AndroidUtilities.dp(8))

                # Chat ID input
                chat_id_input = self._create_styled_edit_text(context, "Chat ID (from recent messages)")
                layout.addView(chat_id_input)

                # Space
                space1 = View(context)
                space1.setLayoutParams(LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(12)))
                layout.addView(space1)

                # Message input
                message_input = self._create_styled_edit_text(context, "Enter your message...")
                layout.addView(message_input)

                # Recent chats info
                space2 = View(context)
                space2.setLayoutParams(LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(12)))
                layout.addView(space2)

                info_text = TextView(context)
                recent_chats_info = self._get_recent_chats_info()
                info_text.setText(recent_chats_info)
                info_text.setTextColor(Theme.SECONDARY_TEXT_COLOR)
                info_text.setTextSize(12)
                layout.addView(info_text)

                # Buttons
                buttons_layout = LinearLayout(context)
                buttons_layout.setOrientation(LinearLayout.HORIZONTAL)
                buttons_layout.setGravity(Gravity.END)
                buttons_layout.setPadding(0, AndroidUtilities.dp(16), 0, 0)

                dialog_instance = builder.get_dialog()

                def cancel_action(v):
                    if dialog_instance:
                        dialog_instance.dismiss()

                def send_action(v):
                    chat_id_text = chat_id_input.getText().toString().strip()
                    message_text = message_input.getText().toString().strip()

                    if not chat_id_text or not message_text:
                        BulletinHelper.show_error("❌ Please fill both fields")
                        return

                    try:
                        chat_id = int(chat_id_text)
                        self._send_message_to_chat(chat_id, message_text)
                        if dialog_instance:
                            dialog_instance.dismiss()
                    except ValueError:
                        BulletinHelper.show_error("❌ Invalid chat ID")

                cancel_button = self._create_styled_button(context, "Cancel", False, cancel_action)
                send_button = self._create_styled_button(context, "Send", True, send_action)

                btn_params = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                btn_params.setMarginStart(AndroidUtilities.dp(8))

                buttons_layout.addView(cancel_button)
                buttons_layout.addView(send_button, btn_params)
                layout.addView(buttons_layout)

                builder.set_view(layout)
                dialog_instance = builder.show().get_dialog()
                if dialog_instance and dialog_instance.getWindow():
                    dialog_instance.getWindow().getDecorView().setBackgroundColor(Theme.DIALOG_BG_COLOR)

            except Exception as e:
                log(f"[BotManager] Error creating send dialog: {e}")
                BulletinHelper.show_error(f"❌ Error: {str(e)}")

        run_on_ui_thread(dialog_action)

    def _get_recent_chats_info(self):
        """Get info about recent chats for display"""
        if not self.messages:
            return "No recent chats available"

        recent_chats = self._get_recent_chats()
        if not recent_chats:
            return "No recent chats available"

        info_lines = ["Recent chats:"]
        for chat_data in recent_chats[-5:]:  # Last 5 chats
            msg = chat_data["message"]
            chat = msg.get("chat", {})
            sender = msg.get("from", {})
            chat_id = chat.get("id")

            if chat.get("type") == "private":
                name = sender.get("first_name", "Unknown")
                if sender.get("username"):
                    name += f" (@{sender['username']})"
                info_lines.append(f"• {chat_id}: {name}")
            else:
                title = chat.get("title", "Group Chat")
                info_lines.append(f"• {chat_id}: {title}")

        return "\n".join(info_lines)

    def _show_simple_send_dialog(self):
        """Fallback simple send dialog"""
        try:
            # Get recent chats for quick selection
            recent_chats = self._get_recent_chats()

            if not recent_chats:
                BulletinHelper.show_error("❌ No recent chats found. Wait for incoming messages first.")
                return

            # For now, send to first chat as example
            if recent_chats:
                chat_id = recent_chats[0]["message"].get("chat", {}).get("id")
                if chat_id:
                    self._send_message_to_chat(chat_id, "Hello from Bot Manager! 🤖")

        except Exception as e:
            log(f"[BotManager] Error in simple send dialog: {e}")
            BulletinHelper.show_error(f"❌ Error: {str(e)}")

    def _get_recent_chats(self):
        """Get list of recent chats from messages"""
        chat_map = {}
        for msg_data in self.messages:
            msg = msg_data["message"]
            chat_id = msg.get("chat", {}).get("id")
            if chat_id and chat_id not in chat_map:
                chat_map[chat_id] = msg_data
        return list(chat_map.values())

    def _show_chat_selection(self, chat_options: List[str], chat_ids: List[int]):
        """Show dialog to select chat for sending message"""
        try:
            from ui.alert import AlertDialogBuilder

            dialog_text = "Select chat to send message:\n\n"
            for i, option in enumerate(chat_options):
                dialog_text += f"{i+1}. {option}\n"

            dialog_text += f"\nEnter number (1-{len(chat_options)}):"

            # For now, send to first chat as example
            # In a full implementation, you would create a proper selection dialog
            if chat_ids:
                self._send_message_to_chat(chat_ids[0], "Hello from Bot Manager! 🤖")

        except Exception as e:
            log(f"[BotManager] Error in chat selection: {e}")

    def _send_message_to_chat(self, chat_id: int, text: str):
        """Send message to specific chat"""
        def send():
            try:
                url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
                data = {
                    "chat_id": chat_id,
                    "text": text
                }

                response = requests.post(url, json=data, timeout=10)

                if response.status_code == 200:
                    result = response.json()
                    if result.get("ok"):
                        # Store sent message
                        sent_message = result.get("result", {})
                        self._handle_outgoing_message(sent_message, text)

                        run_on_ui_thread(lambda: BulletinHelper.show_success("✅ Message sent successfully"))
                        log(f"[BotManager] Message sent to chat {chat_id}: {text[:50]}")
                    else:
                        error_msg = result.get("description", "Unknown error")
                        run_on_ui_thread(lambda: BulletinHelper.show_error(f"❌ Send error: {error_msg}"))
                        log(f"[BotManager] Send error: {error_msg}")
                else:
                    run_on_ui_thread(lambda: BulletinHelper.show_error(f"❌ HTTP error: {response.status_code}"))
                    log(f"[BotManager] HTTP error {response.status_code} when sending message")

            except Exception as e:
                log(f"[BotManager] Error sending message: {e}")
                run_on_ui_thread(lambda: BulletinHelper.show_error(f"❌ Send error: {str(e)}"))

        run_on_queue(send)

    def send_custom_message(self, chat_id: int, text: str):
        """Public method to send custom message (can be called from other plugins)"""
        if not self.bot_token:
            log("[BotManager] Bot token not configured")
            return False

        self._send_message_to_chat(chat_id, text)
        return True

    def get_bot_messages(self) -> List[Dict[str, Any]]:
        """Get all bot messages (can be called from other plugins)"""
        return self.messages.copy()

    def get_bot_status(self) -> Dict[str, Any]:
        """Get bot status information"""
        return {
            "is_polling": self.is_polling,
            "message_count": len(self.messages),
            "bot_info": self.bot_info,
            "last_update_id": self.last_update_id
        }

# Create plugin instance
plugin = BotManagerPlugin()
