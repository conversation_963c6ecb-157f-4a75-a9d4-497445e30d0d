# Bot Manager Plugin - Руководство по тестированию

## Исправления в интерфейсе (v1.1)

Плагин был обновлен с учетом правильной реализации UI, как в `sotsialnoe-mnenie-v1.3.plugin`:

### Ключевые исправления:

1. **Правильный вызов `AlertDialogBuilder(context)`** - только с контекстом
2. **Корректная последовательность создания диалогов**:
   - `builder.get_dialog()` → настройка кнопок → `builder.show().get_dialog()`
3. **Исправлена ошибка "Stats" кнопки**
4. **Улучшена обработка ошибок UI**
5. **Добавлены fallback методы для простых диалогов**

## Поток сообщений (ВАЖНО!)

### 📥 Входящие сообщения (INCOMING):
- **Источник**: Пользователи отправляют сообщения **вашему боту**
- **Получение**: Через Telegram Bot API (getUpdates)
- **Обработка**: Плагин получает и может автоматически отвечать

### 📤 Исходящие сообщения (OUTGOING):
- **Источник**: Ваш бот отправляет сообщения пользователям
- **Отправка**: Через Telegram Bot API (sendMessage)
- **Обработка**: Плагин отправляет и сохраняет в истории

### ❌ Что плагин НЕ делает:
- НЕ перехватывает сообщения из обычных Telegram чатов
- НЕ читает ваши личные сообщения
- НЕ обрабатывает сообщения других ботов (по умолчанию)

## Как протестировать

### 1. Установка
```bash
# Скопируйте файлы в папку Plugins
cp bot_manager.plugin /path/to/exteraGram/Plugins/
cp bot_interface_helper.py /path/to/exteraGram/Plugins/
```

### 2. Настройка бота
1. Создайте бота через @BotFather
2. Получите токен (формат: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)
3. Откройте настройки exteraGram → Плагины → Bot Manager
4. Вставьте токен в поле "Bot Token"

### 3. Тестирование подключения
1. Нажмите "Test Bot Connection"
2. Должно появиться сообщение с именем бота
3. Если ошибка - проверьте токен

### 4. Запуск polling
1. Нажмите "Start Polling" или включите "Auto Start Polling"
2. **ВАЖНО**: Отправьте сообщение **вашему боту** из другого аккаунта/устройства
3. Должно появиться уведомление о новом сообщении
4. Проверьте логи: `[BotManager] Incoming message: UserName (ID: 123456) in private chat...`

### 5. Тестирование интерфейса
1. Нажмите "Open Bot Interface"
2. Должно открыться окно с:
   - Информацией о боте
   - Статусом polling
   - Списком сообщений
   - Кнопками управления

### 6. Отправка сообщений
1. В интерфейсе нажмите "Send Message"
2. Введите Chat ID (из списка недавних чатов)
3. Введите текст сообщения
4. Нажмите "Send"

## Возможные проблемы и решения

### Интерфейс не открывается
- **Причина**: Android UI классы недоступны
- **Решение**: Плагин автоматически переключится на простой интерфейс
- **Проверка**: Посмотрите логи на наличие `[BotManager] Android UI classes not available`

### Ошибка "Bot token not configured"
- **Причина**: Токен не введен или неверный
- **Решение**: Проверьте токен в настройках плагина

### Сообщения не приходят
- **Причина**: Polling не запущен или проблемы с сетью
- **Решение**: 
  1. Проверьте статус polling в интерфейсе
  2. Перезапустите polling
  3. Проверьте интернет-соединение

### Не удается отправить сообщение
- **Причина**: Неверный Chat ID или нет прав
- **Решение**:
  1. Используйте Chat ID из списка недавних чатов
  2. Убедитесь что бот может писать в этот чат

## Логирование

Плагин записывает подробные логи с префиксом `[BotManager]`:
- Загрузка/выгрузка плагина
- Статус polling
- Получение/отправка сообщений
- Ошибки UI и сети

## Структура UI

### Основной интерфейс:
```
🤖 Bot Manager
├── Статус бота (имя, username)
├── Статус polling (Running/Stopped)
├── Количество сообщений и чатов
├── Прокручиваемый список сообщений
└── Кнопки: [Send Message] [Stats] [Close]
```

### Диалог отправки:
```
📤 Send Message
├── Поле ввода Chat ID
├── Поле ввода сообщения
├── Список недавних чатов
└── Кнопки: [Cancel] [Send]
```

### Статистика:
```
📊 Detailed Bot Statistics
├── Общая статистика
├── Информация о боте
├── Разбивка по типам сообщений
└── Кнопка: [OK]
```

## API для других плагинов

```python
# Получить экземпляр плагина
bot_manager = plugin_manager.get_plugin("bot_manager")

# Отправить сообщение
success = bot_manager.send_custom_message(chat_id, "Привет!")

# Получить все сообщения
messages = bot_manager.get_bot_messages()

# Получить статус
status = bot_manager.get_bot_status()
# Возвращает: {
#   "is_polling": bool,
#   "message_count": int,
#   "bot_info": dict,
#   "last_update_id": int
# }
```

## Автоматические команды

Плагин автоматически отвечает на команды (если включено в настройках):
- `/start` - приветствие
- `/help` - список команд
- `/stats` - статистика бота
- `/echo <текст>` - повтор текста
- `/time` - текущее время

## Следующие шаги

После успешного тестирования можно:
1. Добавить больше автоматических команд
2. Реализовать webhook вместо polling
3. Добавить поддержку медиа-сообщений
4. Создать более сложный UI с RecyclerView
5. Добавить экспорт истории сообщений

---

**Важно**: Плагин использует Telegram Bot API, который отличается от обычного Telegram API. Убедитесь что используете правильный токен от @BotFather.
