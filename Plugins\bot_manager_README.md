# Bot Manager Plugin для exteraGram

Плагин для управления Telegram ботом с отдельным интерфейсом для получения и отправки сообщений.

## Возможности

- 🤖 **Авторизация через токен бота** - подключение к любому Telegram боту
- 📨 **Получение сообщений** - автоматическое получение сообщений от пользователей
- 📤 **Отправка ответов** - возможность отвечать на сообщения
- 🖥️ **Отдельный интерфейс** - удобный интерфейс для управления ботом
- 🔄 **Автоматические команды** - встроенные команды (/start, /help, /stats и др.)
- 📊 **Статистика** - детальная статистика работы бота
- 🔔 **Уведомления** - уведомления о новых сообщениях

## Установка

1. Скопируйте файлы `bot_manager.plugin` и `bot_interface_helper.py` в папку `Plugins/`
2. Перезапустите exteraGram или перезагрузите плагины
3. Включите плагин в настройках

## Настройка

### Получение токена бота

1. Найдите [@BotFather](https://t.me/botfather) в Telegram
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Скопируйте полученный токен (формат: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### Настройка плагина

1. Откройте настройки exteraGram → Плагины → Bot Manager
2. Вставьте токен бота в поле "Bot Token"
3. Настройте дополнительные параметры:
   - **Auto Start Polling** - автоматический запуск при загрузке плагина
   - **Show Message Notifications** - показывать уведомления о новых сообщениях
   - **Auto-respond to Commands** - автоматически отвечать на команды

## Использование

### Тестирование подключения

1. В настройках плагина нажмите "Test Bot Connection"
2. Если подключение успешно, вы увидите информацию о боте

### Запуск получения сообщений

1. Нажмите "Start Polling" для начала получения сообщений
2. Или включите "Auto Start Polling" для автоматического запуска

### Управление ботом

1. Нажмите "Open Bot Interface" для открытия интерфейса управления
2. В интерфейсе вы увидите:
   - Статус бота
   - Количество сообщений и чатов
   - Список последних чатов/сообщений
3. Используйте кнопки для отправки сообщений и просмотра статистики

### Отправка сообщений

1. В интерфейсе бота нажмите "Send Message"
2. Выберите чат из списка недавних
3. Сообщение будет отправлено автоматически (в текущей версии - тестовое сообщение)

## Встроенные команды бота

Плагин автоматически отвечает на следующие команды:

- `/start` - приветствие и информация о боте
- `/help` - список доступных команд
- `/stats` - статистика бота (количество чатов, сообщений)
- `/echo <текст>` - повторить отправленный текст
- `/time` - показать текущее время

## API для других плагинов

Плагин предоставляет API для взаимодействия с другими плагинами:

```python
# Получить экземпляр плагина
bot_manager = plugin_manager.get_plugin("bot_manager")

# Отправить сообщение
bot_manager.send_custom_message(chat_id, "Привет!")

# Получить все сообщения
messages = bot_manager.get_bot_messages()

# Получить статус бота
status = bot_manager.get_bot_status()
```

## Структура файлов

- `bot_manager.plugin` - основной файл плагина
- `bot_interface_helper.py` - вспомогательные классы для расширенного интерфейса
- `bot_manager_README.md` - данная документация

## Технические детали

### Архитектура

Плагин использует:
- **Telegram Bot API** для взаимодействия с ботом
- **HTTP polling** для получения обновлений
- **Threading** для неблокирующей работы
- **exteraGram Plugin API** для интеграции с приложением

### Безопасность

- Токен бота хранится в настройках плагина
- Все HTTP запросы используют HTTPS
- Обработка ошибок и таймауты для стабильной работы

### Ограничения

- Polling интервал: 1 секунда
- Максимум 10 обновлений за запрос
- Таймаут HTTP запросов: 10 секунд
- Интерфейс использует простые диалоги (ограничение платформы)

## Устранение неполадок

### Бот не получает сообщения

1. Проверьте правильность токена
2. Убедитесь что polling запущен
3. Проверьте интернет-соединение
4. Посмотрите логи в exteraGram

### Ошибки авторизации

1. Проверьте формат токена (должен содержать `:`)
2. Убедитесь что бот не заблокирован
3. Проверьте что токен действителен в @BotFather

### Проблемы с интерфейсом

1. Перезагрузите плагин
2. Проверьте что файл `bot_interface_helper.py` находится в папке Plugins
3. Посмотрите логи для диагностики

## Планы развития

- 📱 Полноценный UI с списком чатов и сообщений
- 🔧 Настраиваемые команды бота
- 📁 Экспорт истории сообщений
- 🔌 Webhook поддержка
- 🎨 Кастомизация интерфейса
- 📊 Расширенная аналитика

## Поддержка

Если у вас возникли проблемы или предложения:

1. Проверьте логи exteraGram
2. Убедитесь что все файлы плагина на месте
3. Попробуйте перезагрузить плагин
4. Обратитесь к разработчику плагина

---

**Версия:** 1.0.0  
**Автор:** @exteraDev  
**Совместимость:** exteraGram 11.12.1+
