"""
Helper module for Bot Manager Plugin
Provides enhanced UI components and utilities for bot management
"""

import json
import time
from typing import Dict, Any, List, Optional, Callable
from android_utils import log, run_on_ui_thread

class BotMessage:
    """Represents a bot message with enhanced functionality"""
    
    def __init__(self, message_data: Dict[str, Any], message_type: str = "incoming"):
        self.data = message_data
        self.type = message_type  # "incoming" or "outgoing"
        self.timestamp = time.time()
        
    @property
    def text(self) -> str:
        """Get message text"""
        return self.data.get("text", "[Media/No text]")
        
    @property
    def sender_name(self) -> str:
        """Get sender name"""
        if self.type == "outgoing":
            return "Bot"
        sender = self.data.get("from", {})
        name = sender.get("first_name", "Unknown")
        if sender.get("last_name"):
            name += f" {sender['last_name']}"
        return name
        
    @property
    def sender_username(self) -> str:
        """Get sender username"""
        if self.type == "outgoing":
            return ""
        return self.data.get("from", {}).get("username", "")
        
    @property
    def chat_id(self) -> int:
        """Get chat ID"""
        return self.data.get("chat", {}).get("id", 0)
        
    @property
    def chat_title(self) -> str:
        """Get chat title"""
        chat = self.data.get("chat", {})
        if chat.get("type") == "private":
            return self.sender_name
        return chat.get("title", "Unknown Chat")
        
    @property
    def formatted_time(self) -> str:
        """Get formatted timestamp"""
        return time.strftime("%H:%M:%S", time.localtime(self.timestamp))
        
    def to_display_string(self) -> str:
        """Convert to display string"""
        prefix = "📤" if self.type == "outgoing" else "📥"
        return f"{prefix} [{self.formatted_time}] {self.sender_name}: {self.text[:100]}"

class BotChat:
    """Represents a chat with the bot"""
    
    def __init__(self, chat_id: int, chat_data: Dict[str, Any]):
        self.chat_id = chat_id
        self.data = chat_data
        self.messages: List[BotMessage] = []
        self.last_activity = time.time()
        
    @property
    def title(self) -> str:
        """Get chat title"""
        if self.data.get("type") == "private":
            name = self.data.get("first_name", "Unknown")
            if self.data.get("last_name"):
                name += f" {self.data['last_name']}"
            return name
        return self.data.get("title", "Unknown Chat")
        
    @property
    def username(self) -> str:
        """Get chat username"""
        return self.data.get("username", "")
        
    @property
    def message_count(self) -> int:
        """Get message count"""
        return len(self.messages)
        
    def add_message(self, message: BotMessage):
        """Add message to chat"""
        self.messages.append(message)
        self.last_activity = time.time()
        
    def get_recent_messages(self, limit: int = 10) -> List[BotMessage]:
        """Get recent messages"""
        return self.messages[-limit:] if self.messages else []

class BotInterface:
    """Enhanced bot interface manager"""
    
    def __init__(self, plugin_instance):
        self.plugin = plugin_instance
        self.chats: Dict[int, BotChat] = {}
        self.selected_chat_id: Optional[int] = None
        
    def process_message(self, message_data: Dict[str, Any], message_type: str = "incoming"):
        """Process incoming or outgoing message"""
        try:
            message = BotMessage(message_data, message_type)
            chat_id = message.chat_id
            
            # Create or get chat
            if chat_id not in self.chats:
                chat_data = message_data.get("chat", {})
                self.chats[chat_id] = BotChat(chat_id, chat_data)
                
            # Add message to chat
            self.chats[chat_id].add_message(message)
            
            log(f"[BotInterface] Processed {message_type} message in chat {chat_id}")
            
        except Exception as e:
            log(f"[BotInterface] Error processing message: {e}")
            
    def get_chat_list(self) -> List[BotChat]:
        """Get list of chats sorted by last activity"""
        return sorted(self.chats.values(), key=lambda c: c.last_activity, reverse=True)
        
    def get_chat(self, chat_id: int) -> Optional[BotChat]:
        """Get specific chat"""
        return self.chats.get(chat_id)
        
    def select_chat(self, chat_id: int):
        """Select chat for interaction"""
        if chat_id in self.chats:
            self.selected_chat_id = chat_id
            
    def get_selected_chat(self) -> Optional[BotChat]:
        """Get currently selected chat"""
        if self.selected_chat_id:
            return self.chats.get(self.selected_chat_id)
        return None
        
    def send_message(self, chat_id: int, text: str, callback: Optional[Callable] = None):
        """Send message to chat"""
        try:
            if not self.plugin.bot_token:
                if callback:
                    callback(False, "Bot token not configured")
                return
                
            # Use plugin's send method
            success = self.plugin.send_custom_message(chat_id, text)
            
            if callback:
                callback(success, "Message sent" if success else "Failed to send")
                
        except Exception as e:
            log(f"[BotInterface] Error sending message: {e}")
            if callback:
                callback(False, str(e))
                
    def get_stats(self) -> Dict[str, Any]:
        """Get interface statistics"""
        total_messages = sum(chat.message_count for chat in self.chats.values())
        return {
            "total_chats": len(self.chats),
            "total_messages": total_messages,
            "active_chats": len([c for c in self.chats.values() if c.last_activity > time.time() - 3600]),
            "selected_chat": self.selected_chat_id
        }
        
    def export_chat_history(self, chat_id: int) -> str:
        """Export chat history as text"""
        chat = self.get_chat(chat_id)
        if not chat:
            return ""
            
        lines = [f"Chat History: {chat.title}"]
        lines.append(f"Chat ID: {chat_id}")
        lines.append(f"Messages: {chat.message_count}")
        lines.append("-" * 50)
        
        for message in chat.messages:
            lines.append(f"[{message.formatted_time}] {message.sender_name}: {message.text}")
            
        return "\n".join(lines)
        
    def clear_chat_history(self, chat_id: int):
        """Clear chat history"""
        if chat_id in self.chats:
            self.chats[chat_id].messages.clear()
            log(f"[BotInterface] Cleared history for chat {chat_id}")

class BotCommands:
    """Bot command processor"""
    
    def __init__(self, interface: BotInterface):
        self.interface = interface
        self.commands = {
            "/start": self._cmd_start,
            "/help": self._cmd_help,
            "/stats": self._cmd_stats,
            "/echo": self._cmd_echo,
            "/time": self._cmd_time,
        }
        
    def process_command(self, message: BotMessage) -> Optional[str]:
        """Process bot command and return response"""
        text = message.text.strip()
        if not text.startswith("/"):
            return None
            
        parts = text.split(" ", 1)
        command = parts[0].lower()
        args = parts[1] if len(parts) > 1 else ""
        
        if command in self.commands:
            try:
                return self.commands[command](message, args)
            except Exception as e:
                log(f"[BotCommands] Error processing command {command}: {e}")
                return f"Error processing command: {str(e)}"
                
        return f"Unknown command: {command}. Type /help for available commands."
        
    def _cmd_start(self, message: BotMessage, args: str) -> str:
        """Start command"""
        return f"Hello {message.sender_name}! 👋\n\nI'm managed by exteraGram Bot Manager plugin.\nType /help to see available commands."
        
    def _cmd_help(self, message: BotMessage, args: str) -> str:
        """Help command"""
        help_text = "Available commands:\n\n"
        help_text += "/start - Start interaction\n"
        help_text += "/help - Show this help\n"
        help_text += "/stats - Show bot statistics\n"
        help_text += "/echo <text> - Echo your message\n"
        help_text += "/time - Show current time\n"
        return help_text
        
    def _cmd_stats(self, message: BotMessage, args: str) -> str:
        """Stats command"""
        stats = self.interface.get_stats()
        return f"📊 Bot Statistics:\n\nTotal chats: {stats['total_chats']}\nTotal messages: {stats['total_messages']}\nActive chats: {stats['active_chats']}"
        
    def _cmd_echo(self, message: BotMessage, args: str) -> str:
        """Echo command"""
        if not args:
            return "Please provide text to echo. Usage: /echo <your text>"
        return f"🔄 Echo: {args}"
        
    def _cmd_time(self, message: BotMessage, args: str) -> str:
        """Time command"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        return f"🕐 Current time: {current_time}"

def create_bot_interface(plugin_instance) -> BotInterface:
    """Factory function to create bot interface"""
    return BotInterface(plugin_instance)
