"""
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  ███╗   ███╗ █████╗ ███╗   ██╗██████╗ ██████╗ ███████╗       ║
║  ████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔══██╗██╔════╝       ║
║  ██╔████╔██║███████║██╔██╗ ██║██║  ██║██████╔╝█████╗         ║
║  ██║╚██╔╝██║██╔══██║██║╚██╗██║██║  ██║██╔══██╗██╔══╝         ║
║  ██║ ╚═╝ ██║██║  ██║██║ ╚████║██████╔╝██║  ██║███████╗       ║
║  ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═════╝ ╚═╝  ╚═╝╚══════╝       ║
║                                                              ║  
║         █████╗ ██╗                                           ║
║        ██╔══██╗██║                                           ║
║        ███████║██║    © 2024-2025                            ║
║        ██╔══██║██║    Licensed Product                       ║
║        ██║  ██║██║    All Rights Reserved                    ║
║        ╚═╝  ╚═╝╚═╝                                           ║
║                                                              ║
║    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░║
║    ░ Unauthorized use, reproduction or distribution ░        ║
║    ░ of this software is strictly prohibited        ░        ║
║    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
# -*- coding: utf-8 -*-

# --- Metadata ---
__id__ = "user_reviews_svaganetut"
__name__ = "Social Opinion"
__description__ = """
Вам нагрубил человек / сделал работу как надо / вы хотите поделиться информацией о нем или, наоборот, посмотреть отзывы и дополнительную информацию, ПРОВЕРЕННУЮ администрацией плагина? Тогда данный плагин точно для вас!

Someone was rude to you / did their job well / you want to share information about them or, conversely, check reviews and additional information VERIFIED by the plugin administration? Then this plugin is definitely for you!
"""
__author__ = "СвагаНеТута @swagnonher"
__version__ = "1.2" # Incremented version for the fix
__min_version__ = "11.12.0"
__icon__ = "Swagapluginsicon/2"

# --- Imports ---
import requests
import json
import traceback
from typing import Optional, Callable

# ExteraGram imports
from base_plugin import BasePlugin, MenuItemData, MenuItemType
from client_utils import get_last_fragment, get_user_config, run_on_queue
from android_utils import run_on_ui_thread, log
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper

# Java imports for custom dialogs
try:
    from hook_utils import find_class
    from java import dynamic_proxy
    from org.telegram.messenger import AndroidUtilities
    
    EditText = find_class("android.widget.EditText")
    LinearLayout = find_class("android.widget.LinearLayout")
    LayoutParams = find_class("android.widget.LinearLayout$LayoutParams")
    TextView = find_class("android.widget.TextView")
    Button = find_class("android.widget.Button")
    ScrollView = find_class("android.widget.ScrollView")
    View = find_class("android.view.View")
    Gravity = find_class("android.view.Gravity")
    Color = find_class("android.graphics.Color")
    Typeface = find_class("android.graphics.Typeface")
    GradientDrawable = find_class("android.graphics.drawable.GradientDrawable")
    StateListDrawable = find_class("android.graphics.drawable.StateListDrawable")
    Html = find_class("android.text.Html")
    LinkMovementMethod = find_class("android.text.method.LinkMovementMethod")
    
except ImportError:
    EditText, LinearLayout, LayoutParams, TextView, ScrollView, Color, Button, View, Gravity, Typeface, GradientDrawable, StateListDrawable, dynamic_proxy, AndroidUtilities, Html, LinkMovementMethod = [None] * 16

# --- Constants ---
SERVER_URL = "http://node69.lunes.host:3072"

# --- Theme & Style Constants ---
class Theme:
    DIALOG_BG_COLOR = Color.rgb(30, 35, 40)
    PRIMARY_TEXT_COLOR = Color.WHITE
    SECONDARY_TEXT_COLOR = Color.rgb(170, 170, 170)
    HINT_COLOR = Color.rgb(120, 120, 120)
    SEPARATOR_COLOR = Color.rgb(60, 65, 70)
    
    INPUT_BG_COLOR = Color.rgb(45, 50, 55)
    INPUT_STROKE_COLOR = Color.rgb(80, 85, 90)

    ACCENT_BLUE = Color.rgb(65, 150, 240)
    ACCENT_BLUE_PRESSED = Color.rgb(85, 170, 255)
    
    BUTTON_SECONDARY_BG = Color.rgb(50, 55, 60)
    BUTTON_SECONDARY_BG_PRESSED = Color.rgb(70, 75, 80)
    
    CARD_BG_COLOR = Color.rgb(50, 55, 60)
    RATING_BG_COLOR = Color.rgb(40, 45, 50)
    STAR_COLOR = Color.rgb(255, 215, 0)  # Gold color for stars

# --- Click Listener Proxy ---
class _ClickListener(dynamic_proxy(View.OnClickListener)):
    def __init__(self, handler_func: Callable):
        super().__init__()
        self.handler = handler_func

    def onClick(self, view: View):
        self.handler(view)

class UserReviewsPlugin(BasePlugin):
    """
    Plugin for a user review and information system with rating support.
    Adds buttons to the profile menu to interact with the server.
    """
    def on_plugin_load(self):
        self.log("User Reviews plugin with rating system loaded.")
        self._add_profile_menu_items()

    def _add_profile_menu_items(self):
        menu_items = [
            MenuItemData(
                menu_type=MenuItemType.PROFILE_ACTION_MENU,
                text="Оставить отзыв",
                icon="msg_edit",
                on_click=self._handle_leave_review
            ),
            MenuItemData(
                menu_type=MenuItemType.PROFILE_ACTION_MENU,
                text="Посмотреть отзывы",
                icon="msg_viewreplies",
                on_click=self._handle_view_reviews
            ),
            MenuItemData(
                menu_type=MenuItemType.PROFILE_ACTION_MENU,
                text="Добавить информацию",
                icon="msg_addfolder",
                on_click=self._handle_add_info
            ),
            MenuItemData(
                menu_type=MenuItemType.PROFILE_ACTION_MENU,
                text="Посмотреть информацию",
                icon="msg_info",
                on_click=self._handle_view_info
            )
        ]
        for item in menu_items:
            self.add_menu_item(item)

    # --- Menu Button Handlers ---

    def _handle_leave_review(self, context: dict):
        target_user_id = self._get_user_id_from_context(context)
        if not target_user_id: return

        self._show_rating_input_dialog(
            title="Оставить отзыв",
            hint="Введите ваш отзыв...",
            button_text="Отправить",
            callback=lambda text, rating: self._submit_data(
                endpoint="/review",
                payload={"text": text, "rating": rating},
                target_user_id=target_user_id,
                success_message="Отзыв успешно отправлен!"
            )
        )

    def _handle_add_info(self, context: dict):
        target_user_id = self._get_user_id_from_context(context)
        if not target_user_id: return

        self._show_multiline_input_dialog(
            title="Добавить информацию",
            hint1="Введите информацию о пользователе...",
            hint2="Ссылка на доказательство (пруф)...",
            button_text="Отправить на проверку",
            callback=lambda info, proof: self._submit_data(
                endpoint="/info",
                payload={"text": info, "proof": proof},
                target_user_id=target_user_id,
                success_message="Информация отправлена на проверку!"
            )
        )

    def _handle_view_reviews(self, context: dict):
        target_user_id = self._get_user_id_from_context(context)
        if not target_user_id: return
        
        user = context.get("user")
        title = f"Отзывы о {user.first_name}" if user else "Отзывы о пользователе"
        self._fetch_and_show_data(
            endpoint=f"/reviews/{target_user_id}",
            title=title,
            item_type='review'
        )

    def _handle_view_info(self, context: dict):
        target_user_id = self._get_user_id_from_context(context)
        if not target_user_id: return
        
        user = context.get("user")
        title = f"Информация о {user.first_name}" if user else "Информация о пользователе"
        self._fetch_and_show_data(
            endpoint=f"/info/{target_user_id}",
            title=title,
            item_type='info'
        )

    # --- UI Helpers (Native Dialogs) ---
    def _get_button_drawable(self, normal_color, pressed_color):
        state_list = StateListDrawable()
        normal_drawable = GradientDrawable()
        normal_drawable.setColor(normal_color)
        normal_drawable.setCornerRadius(AndroidUtilities.dp(8))
        
        pressed_drawable = GradientDrawable()
        pressed_drawable.setColor(pressed_color)
        pressed_drawable.setCornerRadius(AndroidUtilities.dp(8))
        
        state_list.addState([16842919], pressed_drawable) # android.R.attr.state_pressed
        state_list.addState([], normal_drawable)
        return state_list

    def _create_styled_button(self, context, text: str, is_primary: bool, on_click: Callable):
        button = Button(context)
        button.setText(text)
        button.setOnClickListener(_ClickListener(on_click))
        button.setTextColor(Theme.PRIMARY_TEXT_COLOR)
        button.setTypeface(None, Typeface.BOLD)
        button.setAllCaps(False)
        button.setPadding(AndroidUtilities.dp(16), AndroidUtilities.dp(12), AndroidUtilities.dp(16), AndroidUtilities.dp(12))

        if is_primary:
            button.setBackground(self._get_button_drawable(Theme.ACCENT_BLUE, Theme.ACCENT_BLUE_PRESSED))
        else:
            button.setBackground(self._get_button_drawable(Theme.BUTTON_SECONDARY_BG, Theme.BUTTON_SECONDARY_BG_PRESSED))
        return button

    def _create_styled_edit_text(self, context, hint: str):
        input_field = EditText(context)
        input_field.setHint(hint)
        input_field.setTextColor(Theme.PRIMARY_TEXT_COLOR)
        input_field.setHintTextColor(Theme.HINT_COLOR)
        input_field.setPadding(AndroidUtilities.dp(12), AndroidUtilities.dp(10), AndroidUtilities.dp(12), AndroidUtilities.dp(10))
        
        bg = GradientDrawable()
        bg.setColor(Theme.INPUT_BG_COLOR)
        bg.setCornerRadius(AndroidUtilities.dp(8))
        bg.setStroke(AndroidUtilities.dp(1), Theme.INPUT_STROKE_COLOR)
        input_field.setBackground(bg)
        
        return input_field

    def _create_rating_selector(self, context):
        """Создает виджет выбора рейтинга (звезды)."""
        rating_layout = LinearLayout(context)
        rating_layout.setOrientation(LinearLayout.HORIZONTAL)
        rating_layout.setGravity(Gravity.CENTER)
        rating_layout.setPadding(0, AndroidUtilities.dp(10), 0, AndroidUtilities.dp(10))
        
        current_rating = [3]  # Default 3 stars
        star_buttons = []

        # Создаем 5 звезд
        for i in range(5):
            star_button = Button(context)
            star_button.setText("★")
            star_button.setTextSize(24)
            star_button.setTextColor(Theme.SECONDARY_TEXT_COLOR)
            star_button.setBackground(None)
            # Уменьшаем горизонтальные отступы, чтобы избежать "слипания"
            star_button.setPadding(AndroidUtilities.dp(2), AndroidUtilities.dp(8), AndroidUtilities.dp(2), AndroidUtilities.dp(8))
            
            def make_star_click_handler(star_index):
                def handler(v):
                    current_rating[0] = star_index + 1
                    for j, btn in enumerate(star_buttons):
                        btn.setTextColor(Theme.STAR_COLOR if j <= star_index else Theme.SECONDARY_TEXT_COLOR)
                return handler
            
            star_button.setOnClickListener(_ClickListener(make_star_click_handler(i)))
            star_buttons.append(star_button)
            
            # --- ГЛАВНОЕ ИСПРАВЛЕНИЕ ---
            # Используем LayoutParams с весом (weight), чтобы гарантировать,
            # что все 5 звезд поместятся в ряд, равномерно разделив пространство.
            button_params = LayoutParams(
                0,  # Ширина 0dp, т.к. она будет рассчитана на основе веса
                LayoutParams.WRAP_CONTENT,  # Высота по содержимому
                1.0  # Вес, каждой кнопке даем равный "кусок" пространства
            )
            rating_layout.addView(star_button, button_params)
        
        # Инициализируем первые 3 звезды как активные
        for i in range(3):
            star_buttons[i].setTextColor(Theme.STAR_COLOR)
        
        return rating_layout, current_rating

    def _show_rating_input_dialog(self, title: str, hint: str, button_text: str, callback: Callable):
        if not EditText:
            run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка: Компоненты UI не найдены."))
            return

        def dialog_action():
            try:
                fragment = get_last_fragment()
                context = fragment.getParentActivity() if fragment else None
                if not context: return

                layout = LinearLayout(context)
                layout.setOrientation(LinearLayout.VERTICAL)
                padding = AndroidUtilities.dp(20)
                layout.setPadding(padding, AndroidUtilities.dp(16), padding, AndroidUtilities.dp(8))

                input_field = self._create_styled_edit_text(context, hint)
                layout.addView(input_field)
                
                space = View(context)
                space.setLayoutParams(LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(15)))
                layout.addView(space)

                rating_label = TextView(context)
                rating_label.setText("Оценка:")
                rating_label.setTextColor(Theme.PRIMARY_TEXT_COLOR)
                rating_label.setTextSize(16)
                rating_label.setTypeface(None, Typeface.BOLD)
                layout.addView(rating_label)

                rating_selector, current_rating = self._create_rating_selector(context)
                layout.addView(rating_selector)

                buttons_layout = LinearLayout(context)
                buttons_layout.setOrientation(LinearLayout.HORIZONTAL)
                buttons_layout.setGravity(Gravity.END)
                buttons_layout.setPadding(0, AndroidUtilities.dp(16), 0, 0)

                builder = AlertDialogBuilder(context)
                dialog_instance = builder.get_dialog()

                def cancel_action(v):
                    if dialog_instance: dialog_instance.dismiss()
                
                def submit_action(v):
                    callback(input_field.getText().toString(), current_rating[0])
                    if dialog_instance: dialog_instance.dismiss()

                cancel_button = self._create_styled_button(context, "Отмена", False, cancel_action)
                submit_button = self._create_styled_button(context, button_text, True, submit_action)
                
                btn_params_cancel = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                btn_params_submit = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                btn_params_submit.setMarginStart(AndroidUtilities.dp(8))

                buttons_layout.addView(cancel_button, btn_params_cancel)
                buttons_layout.addView(submit_button, btn_params_submit)
                layout.addView(buttons_layout)

                builder.set_title(title)
                builder.set_view(layout)
                dialog_instance = builder.show().get_dialog()
                if dialog_instance and dialog_instance.getWindow():
                    dialog_instance.getWindow().getDecorView().setBackgroundColor(Theme.DIALOG_BG_COLOR)
            except Exception:
                self.log(f"Error showing rating input dialog: {traceback.format_exc()}")
                run_on_ui_thread(lambda: BulletinHelper.show_error("Не удалось открыть диалог."))
        
        run_on_ui_thread(dialog_action)

    def _show_input_dialog(self, title: str, hint: str, button_text: str, callback: Callable):
        if not EditText:
            run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка: Компоненты UI не найдены."))
            return

        def dialog_action():
            try:
                fragment = get_last_fragment()
                context = fragment.getParentActivity() if fragment else None
                if not context: return

                layout = LinearLayout(context)
                layout.setOrientation(LinearLayout.VERTICAL)
                padding = AndroidUtilities.dp(20)
                layout.setPadding(padding, AndroidUtilities.dp(16), padding, AndroidUtilities.dp(8))

                input_field = self._create_styled_edit_text(context, hint)
                layout.addView(input_field)

                buttons_layout = LinearLayout(context)
                buttons_layout.setOrientation(LinearLayout.HORIZONTAL)
                buttons_layout.setGravity(Gravity.END)
                buttons_layout.setPadding(0, AndroidUtilities.dp(16), 0, 0)

                builder = AlertDialogBuilder(context)
                dialog_instance = builder.get_dialog()

                def cancel_action(v):
                    if dialog_instance: dialog_instance.dismiss()
                
                def submit_action(v):
                    callback(input_field.getText().toString())
                    if dialog_instance: dialog_instance.dismiss()

                cancel_button = self._create_styled_button(context, "Отмена", False, cancel_action)
                submit_button = self._create_styled_button(context, button_text, True, submit_action)
                
                btn_params_cancel = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                btn_params_submit = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                btn_params_submit.setMarginStart(AndroidUtilities.dp(8))

                buttons_layout.addView(cancel_button, btn_params_cancel)
                buttons_layout.addView(submit_button, btn_params_submit)
                layout.addView(buttons_layout)

                builder.set_title(title)
                builder.set_view(layout)
                dialog_instance = builder.show().get_dialog()
                if dialog_instance and dialog_instance.getWindow():
                    dialog_instance.getWindow().getDecorView().setBackgroundColor(Theme.DIALOG_BG_COLOR)
            except Exception:
                self.log(f"Error showing input dialog: {traceback.format_exc()}")
                run_on_ui_thread(lambda: BulletinHelper.show_error("Не удалось открыть диалог."))
        
        run_on_ui_thread(dialog_action)

    def _show_multiline_input_dialog(self, title: str, hint1: str, hint2: str, button_text: str, callback: Callable):
        if not EditText:
            run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка: Компоненты UI не найдены."))
            return

        def dialog_action():
            try:
                fragment = get_last_fragment()
                context = fragment.getParentActivity() if fragment else None
                if not context: return

                layout = LinearLayout(context)
                layout.setOrientation(LinearLayout.VERTICAL)
                padding = AndroidUtilities.dp(20)
                layout.setPadding(padding, AndroidUtilities.dp(16), padding, AndroidUtilities.dp(8))

                input_field1 = self._create_styled_edit_text(context, hint1)
                layout.addView(input_field1)
                
                space = View(context)
                space.setLayoutParams(LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(10)))
                layout.addView(space)

                input_field2 = self._create_styled_edit_text(context, hint2)
                layout.addView(input_field2)

                buttons_layout = LinearLayout(context)
                buttons_layout.setOrientation(LinearLayout.HORIZONTAL)
                buttons_layout.setGravity(Gravity.END)
                buttons_layout.setPadding(0, AndroidUtilities.dp(16), 0, 0)
                
                builder = AlertDialogBuilder(context)
                dialog_instance = builder.get_dialog()

                def cancel_action(v):
                    if dialog_instance: dialog_instance.dismiss()

                def submit_action(v):
                    callback(input_field1.getText().toString(), input_field2.getText().toString())
                    if dialog_instance: dialog_instance.dismiss()

                cancel_button = self._create_styled_button(context, "Отмена", False, cancel_action)
                submit_button = self._create_styled_button(context, button_text, True, submit_action)
                
                btn_params_cancel = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                btn_params_submit = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                btn_params_submit.setMarginStart(AndroidUtilities.dp(8))

                buttons_layout.addView(cancel_button, btn_params_cancel)
                buttons_layout.addView(submit_button, btn_params_submit)
                layout.addView(buttons_layout)

                builder.set_title(title)
                builder.set_view(layout)
                dialog_instance = builder.show().get_dialog()
                if dialog_instance and dialog_instance.getWindow():
                    dialog_instance.getWindow().getDecorView().setBackgroundColor(Theme.DIALOG_BG_COLOR)
            except Exception:
                self.log(f"Error showing multiline input dialog: {traceback.format_exc()}")
                run_on_ui_thread(lambda: BulletinHelper.show_error("Не удалось открыть диалог."))
        
        run_on_ui_thread(dialog_action)
    
    def _create_rating_display(self, context, rating_stats):
        """Создает блок отображения рейтинга."""
        rating_container = LinearLayout(context)
        rating_container.setOrientation(LinearLayout.VERTICAL)
        rating_container.setPadding(AndroidUtilities.dp(16), AndroidUtilities.dp(12), AndroidUtilities.dp(16), AndroidUtilities.dp(12))
        
        rating_bg = GradientDrawable()
        rating_bg.setColor(Theme.RATING_BG_COLOR)
        rating_bg.setCornerRadius(AndroidUtilities.dp(8))
        rating_bg.setStroke(AndroidUtilities.dp(1), Theme.SEPARATOR_COLOR)
        rating_container.setBackground(rating_bg)
        
        first_line = LinearLayout(context)
        first_line.setOrientation(LinearLayout.HORIZONTAL)
        first_line.setGravity(Gravity.CENTER_VERTICAL)
        
        stars_text = TextView(context)
        stars_text.setText(rating_stats.get('stars_display', '☆☆☆☆☆'))
        stars_text.setTextColor(Theme.STAR_COLOR)
        stars_text.setTextSize(20)
        stars_text.setTypeface(None, Typeface.BOLD)
        first_line.addView(stars_text)
        
        separator1 = TextView(context)
        separator1.setText(" / ")
        separator1.setTextColor(Theme.SECONDARY_TEXT_COLOR)
        separator1.setTextSize(16)
        separator1.setPadding(AndroidUtilities.dp(8), 0, AndroidUtilities.dp(8), 0)
        first_line.addView(separator1)
        
        percentage_text = TextView(context)
        percentage_text.setText(f"{rating_stats.get('rating_percentage', 0)}%")
        percentage_text.setTextColor(Theme.PRIMARY_TEXT_COLOR)
        percentage_text.setTextSize(16)
        percentage_text.setTypeface(None, Typeface.BOLD)
        first_line.addView(percentage_text)
        
        rating_container.addView(first_line)
        
        second_line = LinearLayout(context)
        second_line.setOrientation(LinearLayout.HORIZONTAL)
        second_line.setGravity(Gravity.CENTER_VERTICAL)
        second_line.setPadding(0, AndroidUtilities.dp(4), 0, 0)
        
        total_reviews = rating_stats.get('total_reviews', 0)
        reviews_word = self._get_reviews_word(total_reviews)
        reviews_text = TextView(context)
        reviews_text.setText(f"{total_reviews} {reviews_word}")
        reviews_text.setTextColor(Theme.SECONDARY_TEXT_COLOR)
        reviews_text.setTextSize(14)
        second_line.addView(reviews_text)
        
        separator2 = TextView(context)
        separator2.setText(" / ")
        separator2.setTextColor(Theme.SECONDARY_TEXT_COLOR)
        separator2.setTextSize(14)
        separator2.setPadding(AndroidUtilities.dp(8), 0, AndroidUtilities.dp(8), 0)
        second_line.addView(separator2)
        
        sentiment_text = TextView(context)
        sentiment_text.setText(rating_stats.get('sentiment', 'Нет отзывов'))
        sentiment_text.setTextColor(Theme.ACCENT_BLUE)
        sentiment_text.setTextSize(14)
        sentiment_text.setTypeface(None, Typeface.BOLD)
        second_line.addView(sentiment_text)
        
        rating_container.addView(second_line)
        
        return rating_container

    def _get_reviews_word(self, count):
        """Возвращает правильное склонение слова 'отзыв'."""
        if count % 10 == 1 and count % 100 != 11:
            return "отзыв"
        elif count % 10 in [2, 3, 4] and count % 100 not in [12, 13, 14]:
            return "отзыва"
        else:
            return "отзывов"
    
    def _show_data_dialog(self, title: str, data: dict, item_type: str):
        def dialog_action():
            try:
                fragment = get_last_fragment()
                context = fragment.getParentActivity() if fragment else None
                if not context: return

                builder = AlertDialogBuilder(context)
                builder.set_title(title)
                
                main_layout = LinearLayout(context)
                main_layout.setOrientation(LinearLayout.VERTICAL)
                
                items = data.get('reviews' if item_type == 'review' else 'info', [])
                rating_stats = data.get('rating_stats', {})
                
                if not items and rating_stats.get('total_reviews', 0) == 0:
                    no_data_text = TextView(context)
                    no_data_text.setText("Здесь пока ничего нет.")
                    no_data_text.setTextColor(Theme.SECONDARY_TEXT_COLOR)
                    no_data_text.setGravity(Gravity.CENTER)
                    no_data_text.setPadding(0, AndroidUtilities.dp(40), 0, AndroidUtilities.dp(40))
                    main_layout.addView(no_data_text)
                else:
                    scroll_view = ScrollView(context)
                    scroll_params = LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(350))
                    scroll_view.setLayoutParams(scroll_params)

                    content_layout = LinearLayout(context)
                    content_layout.setOrientation(LinearLayout.VERTICAL)
                    padding = AndroidUtilities.dp(16)
                    content_layout.setPadding(padding, AndroidUtilities.dp(8), padding, 0)
                    
                    for i, item_data in enumerate(items):
                        card_layout = LinearLayout(context)
                        card_layout.setOrientation(LinearLayout.VERTICAL)
                        card_bg = GradientDrawable()
                        card_bg.setColor(Theme.CARD_BG_COLOR)
                        card_bg.setCornerRadius(AndroidUtilities.dp(12))
                        card_layout.setBackground(card_bg)
                        card_padding = AndroidUtilities.dp(12)
                        card_layout.setPadding(card_padding, card_padding, card_padding, card_padding)
                        
                        card_params = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                        if i > 0:
                            card_params.setMargins(0, AndroidUtilities.dp(8), 0, 0)
                        card_layout.setLayoutParams(card_params)

                        if item_type == 'review':
                            rating = item_data.get('rating', 0)
                            stars = '★' * rating + '☆' * (5 - rating)
                            
                            header_text = f"{stars} Отзыв от {item_data.get('author_user_id', 'Unknown')}:"
                            content_text = item_data.get('text', '')
                            
                            header_view = TextView(context)
                            header_view.setText(header_text)
                            header_view.setTextColor(Theme.SECONDARY_TEXT_COLOR)
                            header_view.setTextSize(14)
                            card_layout.addView(header_view)

                            content_view = TextView(context)
                            content_view.setText(content_text)
                            content_view.setTextColor(Theme.PRIMARY_TEXT_COLOR)
                            content_view.setTextSize(16)
                            content_view.setPadding(0, AndroidUtilities.dp(4), 0, 0)
                            card_layout.addView(content_view)

                        elif item_type == 'info':
                            info_text = f"ℹ️ {item_data.get('text', '')}"
                            proof_url = item_data.get('proof', 'N/A')
                            proof_html = f"🔗 Пруф: <a href='{proof_url}'>{proof_url}</a>"
                            
                            info_view = TextView(context)
                            info_view.setText(info_text)
                            info_view.setTextColor(Theme.PRIMARY_TEXT_COLOR)
                            info_view.setTextSize(16)
                            card_layout.addView(info_view)

                            separator = View(context)
                            sep_params = LayoutParams(LayoutParams.MATCH_PARENT, AndroidUtilities.dp(1))
                            sep_params.setMargins(0, AndroidUtilities.dp(8), 0, AndroidUtilities.dp(8))
                            separator.setLayoutParams(sep_params)
                            separator.setBackgroundColor(Theme.SEPARATOR_COLOR)
                            card_layout.addView(separator)

                            proof_view = TextView(context)
                            if Html and LinkMovementMethod:
                                proof_view.setText(Html.fromHtml(proof_html))
                                proof_view.setMovementMethod(LinkMovementMethod.getInstance())
                            else:
                                proof_view.setText(f"🔗 Пруф: {proof_url}")
                            proof_view.setLinkTextColor(Theme.ACCENT_BLUE)
                            proof_view.setTextSize(14)
                            card_layout.addView(proof_view)

                        content_layout.addView(card_layout)

                    scroll_view.addView(content_layout)
                    main_layout.addView(scroll_view)
                    
                    if rating_stats.get('total_reviews', 0) > 0:
                        rating_display = self._create_rating_display(context, rating_stats)
                        rating_params = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                        rating_params.setMargins(AndroidUtilities.dp(16), AndroidUtilities.dp(8), AndroidUtilities.dp(16), 0)
                        rating_display.setLayoutParams(rating_params)
                        main_layout.addView(rating_display)

                buttons_layout = LinearLayout(context)
                buttons_layout.setOrientation(LinearLayout.HORIZONTAL)
                buttons_layout.setGravity(Gravity.END)
                padding_hor = AndroidUtilities.dp(20)
                buttons_layout.setPadding(padding_hor, AndroidUtilities.dp(16), padding_hor, AndroidUtilities.dp(8))
                
                dialog_instance = builder.get_dialog()
                def close_action(v):
                    if dialog_instance: dialog_instance.dismiss()

                close_button = self._create_styled_button(context, "Закрыть", False, close_action)
                buttons_layout.addView(close_button)
                main_layout.addView(buttons_layout)

                builder.set_view(main_layout)
                dialog_instance = builder.show().get_dialog()
                if dialog_instance and dialog_instance.getWindow():
                    dialog_instance.getWindow().setBackgroundDrawable(None)
            except Exception:
                self.log(f"Error showing data dialog: {traceback.format_exc()}")
                run_on_ui_thread(lambda: BulletinHelper.show_error("Не удалось отобразить данные."))

        run_on_ui_thread(dialog_action)
    
    # --- Server Interaction Logic ---
    
    def _show_spinner(self, title: str) -> Optional[AlertDialogBuilder]:
        fragment = get_last_fragment()
        if not fragment or not fragment.getParentActivity(): return None
        
        spinner = AlertDialogBuilder(fragment.getParentActivity(), AlertDialogBuilder.ALERT_TYPE_SPINNER)
        spinner.set_title(title)
        spinner.set_cancelable(False)
        run_on_ui_thread(spinner.show)
        return spinner

    def _submit_data(self, endpoint: str, payload: dict, target_user_id: int, success_message: str):
        spinner = self._show_spinner("Отправка данных...")
        def network_task():
            try:
                author_user_id = get_user_config().getClientUserId()
                full_payload = { "target_user_id": target_user_id, "author_user_id": author_user_id, **payload }
                response = requests.post(f"{SERVER_URL}{endpoint}", json=full_payload, timeout=20)
                if response.status_code == 200:
                    run_on_ui_thread(lambda: BulletinHelper.show_success(success_message))
                else:
                    error_msg = response.json().get("error", f"Ошибка сервера: {response.status_code}")
                    run_on_ui_thread(lambda: BulletinHelper.show_error(error_msg))
            except requests.exceptions.RequestException as e:
                self.log(f"Network error: {e}")
                run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка сети. Проверьте подключение."))
            except Exception:
                self.log(f"Unknown error: {traceback.format_exc()}")
                run_on_ui_thread(lambda: BulletinHelper.show_error("Произошла неизвестная ошибка."))
            finally:
                if spinner: run_on_ui_thread(spinner.dismiss)
        run_on_queue(network_task)

    def _fetch_and_show_data(self, endpoint: str, title: str, item_type: str):
        spinner = self._show_spinner("Загрузка данных...")
        def network_task():
            try:
                response = requests.get(f"{SERVER_URL}{endpoint}", timeout=20)
                if response.status_code == 200:
                    data = response.json()
                    run_on_ui_thread(lambda: self._show_data_dialog(title, data, item_type))
                else:
                    error_msg = response.json().get("error", f"Ошибка сервера: {response.status_code}")
                    run_on_ui_thread(lambda: BulletinHelper.show_error(error_msg))
            except requests.exceptions.RequestException as e:
                self.log(f"Network error: {e}")
                run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка сети. Проверьте подключение."))
            except Exception:
                self.log(f"Unknown error: {traceback.format_exc()}")
                run_on_ui_thread(lambda: BulletinHelper.show_error("Произошла неизвестная ошибка."))
            finally:
                if spinner: run_on_ui_thread(spinner.dismiss)
        run_on_queue(network_task)

    # --- Utility Methods ---
    def _get_user_id_from_context(self, context: dict) -> Optional[int]:
        user = context.get("user")
        if not user:
            run_on_ui_thread(lambda: BulletinHelper.show_error("Не удалось определить пользователя."))
            return None
        return user.id
